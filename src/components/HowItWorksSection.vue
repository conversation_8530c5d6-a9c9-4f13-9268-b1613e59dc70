<script setup>
const props = defineProps({
  startVisitUrl: {
    type: String,
    required: false,
  },
});

const steps = [
  {
    step: "01",
    title: "Complete Assessment",
    description: "Answer a few simple questions about your health goals and medical history through our secure online assessment.",
    icon: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
    color: "from-blue-50 to-blue-100",
    iconBg: "bg-blue-100",
    iconColor: "text-blue-700",
    stepColor: "text-blue-600"
  },
  {
    step: "02",
    title: "Expert Review",
    description: "Our licensed healthcare professionals review your assessment and create a personalized treatment plan tailored to your needs.",
    icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
    color: "from-amber-50 to-amber-100",
    iconBg: "bg-amber-100",
    iconColor: "text-amber-700",
    stepColor: "text-amber-600"
  },
  {
    step: "03",
    title: "Fast Delivery",
    description: "Your personalized treatment is shipped discreetly to your doorstep with free, fast delivery and ongoing support.",
    icon: "M13 10V3L4 14h7v7l9-11h-7z",
    color: "from-green-50 to-green-100",
    iconBg: "bg-green-100",
    iconColor: "text-green-700",
    stepColor: "text-green-600"
  }
];
</script>

<template>
  <!-- How ConnectRx Works Section -->
  <section class="py-24 bg-neutral-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-20">
        <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
          How ConnectRx Works
        </h2>
        <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
          Get personalized healthcare treatments delivered to your door in three simple steps
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
        <div v-for="(step, index) in steps" :key="step.step" class="relative group">

          <!-- Step Card -->
          <div :class="[
            'relative p-8 rounded-3xl border border-neutral-200 bg-gradient-to-br transition-all duration-300 hover:shadow-lg hover:scale-105',
            step.color
          ]">
            <!-- Step Number -->
            <div :class="[
              'inline-flex items-center justify-center w-12 h-12 rounded-2xl font-bold text-lg mb-6',
              step.stepColor,
              'bg-white/80 backdrop-blur-sm'
            ]">
              {{ step.step }}
            </div>

            <!-- Icon -->
            <div :class="[
              'w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300',
              step.iconBg,
              'group-hover:scale-110'
            ]">
              <svg :class="['w-8 h-8', step.iconColor]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" :d="step.icon"></path>
              </svg>
            </div>

            <!-- Content -->
            <h3 class="text-2xl font-bold text-neutral-900 mb-4">
              {{ step.title }}
            </h3>
            <p class="text-neutral-700 leading-relaxed text-lg">
              {{ step.description }}
            </p>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      <div v-if="props.startVisitUrl" class="text-center mt-16">
        <a :href="props.startVisitUrl"
          class="inline-flex items-center bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg">
          Continue Online Visit
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
        </a>
      </div>
      <div v-else class="text-center mt-16">
        <RouterLink :to="{ name: 'Treatments' }"
          class="inline-flex items-center bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg">
          Get Started
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
        </RouterLink>
      </div>
    </div>
  </section>
</template>
