<script setup>
import { formatCurrency } from '@/lib'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import ProductItem from '@/components/ProductItem.vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { IconArrowRight, IconMoodSad } from '@tabler/icons-vue'
import products from '@/data/products.json'
import HowItWorksSection from '@/components/HowItWorksSection.vue'

const route = useRoute()
const loading = ref(true)

const productDetails = computed(() => {
  if (route.params.slug) {
    return products.find(product => product.slug === route.params.slug)
  }
  return null
})

const relatedProducts = computed(() => {
  if (!productDetails.value) return []
  return products.filter(
    p => p.category_slug === productDetails.value.category_slug && p.slug !== productDetails.value.slug
  )
})

const heroMeta = computed(() => {
  const slug = productDetails.value?.category_slug
  const gradients = {
    'weight-loss': 'from-amber-50 via-orange-50 to-amber-100',
    'sexual-health': 'from-cyan-50 via-cyan-50 to-blue-100',
    'hair-health': 'from-emerald-50 via-emerald-50 to-teal-100',
  }
  return { gradient: gradients[slug] || 'from-neutral-50 via-neutral-50 to-slate-100' }
})

const productFAQs = ref([])

watch(productDetails, (pd) => {
  productFAQs.value = pd?.faqs?.map(faq => ({ ...faq, open: false })) || []
}, { immediate: true })

const toggle = (index) => {
  productFAQs.value = productFAQs.value.map((faq, i) => {
    if (i === index) {
      faq.open = !faq.open;
    } else {
      faq.open = false;
    }
    return faq;
  });
};

onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 800)
})
</script>

<template>
  <div class="bg-neutral-50">
    <Header />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      <!-- Loading State -->
      <div v-if="loading" class="bg-neutral-50 rounded-2xl shadow-sm p-8">
        <div class="animate-pulse">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="w-full md:w-1/2 lg:w-2/5">
              <div class="bg-gray-200 rounded-xl aspect-square"></div>
            </div>
            <div class="w-full md:w-1/2 lg:w-3/5">
              <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div class="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-10 bg-gray-200 rounded w-full mb-6"></div>
              <div class="h-12 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Details -->
      <div v-else-if="productDetails">
        <!-- Hero / Top Overview: full-bleed, no outer card -->
        <div :class="['rounded-2xl p-6 sm:p-8 bg-gradient-to-br', heroMeta.gradient]">
          <!-- Breadcrumbs -->
          <nav class="text-sm text-neutral-700 mb-4 flex items-center gap-1">
            <RouterLink :to="{ name: 'Home' }" class="hover:text-neutral-900">Home</RouterLink>
            <span>/</span>
            <RouterLink :to="{ name: 'Treatments', params: { category_slug: productDetails.category_slug } }"
              class="hover:text-neutral-900">{{ productDetails.category }}</RouterLink>
            <span>/</span>
            <span class="text-neutral-900 font-medium">{{ productDetails.name }}</span>
          </nav>

          <div class="flex flex-col md:flex-row items-center gap-8">
            <!-- Product Image -->
            <div class="w-full md:w-1/2 lg:w-2/5 flex justify-center md:justify-start">
              <img :src="productDetails.product_image" :alt="productDetails.name"
                class="max-w-full max-h-[460px] object-contain" />
            </div>

            <!-- Product Info -->
            <div class="w-full md:w-1/2 lg:w-3/5">
              <h1 class="text-3xl sm:text-5xl font-bold text-neutral-900 mb-3">
                {{ productDetails.name }}
              </h1>

              <div class="flex items-center flex-wrap gap-2 mb-4">
                <span
                  class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium">
                  {{ productDetails.product_type }}
                </span>
                <span v-if="productDetails.stock"
                  class="px-3 py-1 bg-green-50 text-green-700 border border-green-200 rounded-full text-xs font-medium">
                  In Stock
                </span>
                <span v-else
                  class="px-3 py-1 bg-red-50 text-red-700 border border-red-200 rounded-full text-xs font-medium">
                  Out of Stock
                </span>
              </div>

              <p class="text-neutral-800 mb-6 leading-relaxed max-w-2xl">
                {{ productDetails.product_summary }}
              </p>

              <div class="flex items-baseline gap-2 mb-6">
                <span class="text-lg font-semibold text-neutral-900">
                  Starting at {{ formatCurrency(productDetails.starting_price) }}
                </span>
              </div>

              <div class="flex flex-col sm:flex-row gap-3">
                <a :href="productDetails.start_visit_url"
                  class="w-full inline-flex justify-center items-center gap-2 bg-black text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-300 cursor-pointer text-center"
                  :disabled="!productDetails.stock" :class="{ 'opacity-60 cursor-not-allowed': !productDetails.stock }">
                  <span v-if="productDetails.stock">Continue Online Visit</span>
                  <span v-else>Out of Stock</span>
                  <IconArrowRight v-if="productDetails.stock" class="w-5 h-5 me-2" stroke-width="2" />
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="py-12 space-y-12">
          <!-- Product Description -->
          <div class="bg-white rounded-3xl p-8">
            <div class="flex items-center gap-3 mb-6">
              <!-- <div
                class="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                  </path>
                </svg>
              </div> -->
              <h2 class="text-2xl md:text-3xl font-bold text-neutral-900">What is {{ productDetails.name }}?</h2>
            </div>
            <div class="text-neutral-700 prose prose-lg max-w-none leading-relaxed" v-html="productDetails.description">
            </div>
          </div>

          <!-- Benefits -->
          <div v-if="productDetails.benefits"
            class="bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 rounded-3xl p-8">
            <div class="flex items-center gap-3 mb-6">
              <!-- <div
                class="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-200 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div> -->
              <h2 class="text-2xl md:text-3xl font-bold text-green-900">Key Benefits</h2>
            </div>
            <div class="prose prose-lg text-green-800 max-w-none leading-relaxed" v-html="productDetails.benefits">
            </div>
          </div>

          <!-- Safety & Side Effects -->
          <div v-if="productDetails.safety_and_side_effects"
            class="bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100 rounded-3xl p-8">
            <div class="flex items-center gap-3 mb-6">
              <!-- <div
                class="w-12 h-12 bg-gradient-to-br from-amber-100 to-orange-200 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-amber-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                  </path>
                </svg>
              </div> -->
              <h2 class="text-2xl md:text-3xl font-bold text-amber-900">Safety & Side Effects</h2>
            </div>
            <div class="prose prose-lg text-amber-800 max-w-none leading-relaxed"
              v-html="productDetails.safety_and_side_effects"></div>
          </div>

          <!-- Customer Reviews -->
          <div v-if="productDetails.customer_reviews?.length" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16">
            <div class="text-center mb-20">
              <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-3">
                Customer Stories
              </h2>
              <p class="text-xl text-neutral-600">
                Real experiences from our valued customers
              </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div v-for="(testimonial, index) in productDetails.customer_reviews" :key="index"
                class="bg-neutral-50 p-8 rounded-3xl border border-neutral-200">
                <div class="flex items-center mb-6">
                  <div v-for="i in 5" :key="i" class="text-yellow-400 mr-1">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                      </path>
                    </svg>
                  </div>
                </div>
                <p class="font-semibold text-neutral-900 mb-3">
                  {{ testimonial.customer_name }}
                </p>
                <p class="text-neutral-700 leading-relaxed text-lg">
                  "{{ testimonial.review }}"
                </p>
              </div>
            </div>
          </div>

          <!-- How ConnectRx Works -->
          <HowItWorksSection :start-visit-url="productDetails.start_visit_url" />

          <!-- FAQs -->
          <div v-if="productFAQs.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-12 pb-16">
            <div class="lg:sticky lg:top-32">
              <h2 class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6">
                Frequently asked <span class="text-amber-500 italic">questions</span>
              </h2>
              <p class="text-lg text-gray-600 mb-8">
                Find answers to common questions about our medication subscriptions and medical review process.
              </p>
            </div>
            <div class="space-y-4">
              <div v-for="(faq, idx) in productFAQs" :key="idx" class="border border-gray-200 rounded-lg">
                <button @click="toggle(idx)"
                  class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors">
                  <span class="font-semibold text-gray-900">
                    {{ faq.question }}
                  </span>
                  <svg class="w-6 h-6 text-gray-500 transition-transform duration-200"
                    :class="{ 'rotate-45': faq.open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                  </svg>
                </button>
                <div class="overflow-hidden transition-all duration-300 ease-in-out"
                  :class="{ 'max-h-96': faq.open, 'max-h-0': !faq.open }">
                  <div class="px-6 pb-6 text-gray-600">
                    <p>{{ faq.answer }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Related Treatments -->
          <div v-if="relatedProducts.length" class="pt-6 mt-10">
            <h2 class="text-2xl md:text-4xl font-semibold text-neutral-900 text-center mb-8">Related Treatments</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductItem v-for="p in relatedProducts" :key="p.slug" :product="p" />
            </div>
          </div>
        </div>
      </div>

      <!-- Product Not Found -->
      <div v-else class="bg-neutral-50 rounded-2xl shadow-sm p-12 text-center">
        <div class="max-w-md mx-auto">
          <IconMoodSad class="w-16 h-16 text-gray-400 mx-auto mb-4" stroke-width="2" />
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            Treatment not found
          </h3>
          <p class="text-gray-600 mb-6">
            The treatment you are looking for does not exist or has been removed.
          </p>
          <RouterLink :to="{ name: 'Treatments' }"
            class="bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition-all duration-300 cursor-pointer">
            Browse Treatments
          </RouterLink>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>
